<template>
  <div class="grid-menu" :class="[`columns-${dynamicColumns}`, { 'scroll-mode': displayMode === 'scroll' }]">
    <div class="grid-container" ref="gridContainer" @scroll="handleScroll">
      <div v-for="(item, index) in displayMenuItems" :key="index"
           class="grid-item"
           :class="{
             'grid-item-more': item.isMore,
             'grid-item-placeholder': item.isPlaceholder
           }"
        @click="handleItemClick(item, index)">
        <div class="item-content" v-if="!item.isPlaceholder">
          <!-- 图标 -->
          <div class="item-icon">
            <img v-if="item.icon" :src="item.icon" :alt="item.title" />
            <div v-else class="icon-placeholder">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" />
              </svg>
            </div>
          </div>

          <!-- 标题 -->
          <div class="item-title">{{ item.title }}</div>

          <!-- 副标题/描述 -->
          <div v-if="item.subtitle" class="item-subtitle">{{ item.subtitle }}</div>

          <!-- 角标 -->
          <div v-if="item.badge" class="item-badge">{{ item.badge }}</div>
        </div>
      </div>
    </div>

    <!-- 滚动模式下的滚动条 -->
    <div v-if="displayMode === 'scroll' && showScrollbar" class="custom-scrollbar">
      <div class="scrollbar-thumb" :style="{ left: scrollbarLeft + 'px', width: scrollbarWidth + 'px' }"></div>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs, ref, nextTick, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  // 菜单项数据
  items: {
    type: Array,
    default: () => []
  },
  // 列数 (2, 4, 5)
  columns: {
    type: Number,
    default: 5,
    validator: (value) => value === 2 || value === 4 || value === 5
  },
  // 是否显示更多按钮
  showMore: {
    type: Boolean,
    default: true
  },
  // 最大显示数量
  maxItems: {
    type: Number,
    default: 10
  },
  // 显示模式：'grid' 网格模式，'scroll' 滚动模式
  displayMode: {
    type: String,
    default: 'grid',
    validator: (value) => ['grid', 'scroll'].includes(value)
  }
})

const { items, columns, showMore, maxItems, displayMode } = toRefs(props)

const emit = defineEmits(['itemClick', 'moreClick'])

// 滚动相关的响应式数据
const gridContainer = ref(null)
const scrollbarLeft = ref(0)
const scrollbarWidth = ref(0)
const showScrollbar = ref(false)

// 计算菜单项
const menuItems = computed(() => {
  let itemList = [...items.value]

  // 滚动模式下不需要限制数量和更多按钮
  if (displayMode.value === 'scroll') {
    return itemList
  }

  // 网格模式下的原有逻辑
  if (showMore.value && itemList.length > maxItems.value - 1) {
    itemList = itemList.slice(0, maxItems.value - 1)
    itemList.push({
      title: '更多',
      icon: null,
      isMore: true
    })
  } else if (itemList.length > maxItems.value) {
    itemList = itemList.slice(0, maxItems.value)
  }

  return itemList
})

// 计算显示的菜单项（包含占位符）
const displayMenuItems = computed(() => {
  const items = menuItems.value

  // 滚动模式下不需要占位符
  if (displayMode.value === 'scroll') {
    return items
  }

  // 网格模式下，如果当前行不满，需要添加占位符
  const itemCount = items.length
  const cols = columns.value

  // 如果项目数量为0或正好是列数的倍数，不需要占位符
  if (itemCount === 0 || itemCount % cols === 0) {
    return items
  }

  // 计算需要添加的占位符数量
  const placeholdersNeeded = cols - (itemCount % cols)
  const result = [...items]

  // 添加占位符
  for (let i = 0; i < placeholdersNeeded; i++) {
    result.push({
      title: '',
      icon: null,
      isPlaceholder: true
    })
  }

  return result
})

// 动态计算列数
const dynamicColumns = computed(() => {
  // 滚动模式下固定为单行
  if (displayMode.value === 'scroll') {
    return menuItems.value.length
  }

  // 网格模式下始终使用设定的列数，确保宽度均匀
  return columns.value
})

// 计算每个项目的宽度百分比
const itemWidth = computed(() => {
  if (displayMode.value === 'scroll') {
    // 滚动模式下每个项目固定宽度
    return '70px'
  }
  return `${100 / dynamicColumns.value}%`
})

// 处理滚动事件
const handleScroll = () => {
  if (displayMode.value !== 'scroll' || !gridContainer.value) return

  const container = gridContainer.value
  const scrollLeft = container.scrollLeft
  const scrollWidth = container.scrollWidth
  const clientWidth = container.clientWidth

  // 计算滚动条位置和宽度
  const scrollbarTrackWidth = 60 // 滚动条轨道宽度
  const scrollRatio = scrollLeft / (scrollWidth - clientWidth)
  const thumbWidth = Math.max(12, (clientWidth / scrollWidth) * scrollbarTrackWidth)
  const thumbLeft = scrollRatio * (scrollbarTrackWidth - thumbWidth)

  scrollbarLeft.value = thumbLeft
  scrollbarWidth.value = thumbWidth
}

// 更新滚动条显示状态
const updateScrollbarVisibility = () => {
  if (displayMode.value !== 'scroll' || !gridContainer.value) {
    showScrollbar.value = false
    return
  }

  const container = gridContainer.value
  showScrollbar.value = container.scrollWidth > container.clientWidth
}

// 处理项目点击
const handleItemClick = (item, index) => {
  // 占位符不响应点击
  if (item.isPlaceholder) {
    return
  }

  if (item.isMore) {
    emit('moreClick')
  } else {
    emit('itemClick', { item, index })
  }
}

// 监听数据变化
watch([displayMenuItems, displayMode], () => {
  nextTick(() => {
    updateScrollbarVisibility()
    handleScroll()
  })
}, { immediate: true })

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    updateScrollbarVisibility()
    handleScroll()
  })

  if (gridContainer.value) {
    gridContainer.value.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  if (gridContainer.value) {
    gridContainer.value.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style scoped lang="less">
.grid-menu {
  padding: @padding-page;
  //background: @bg-color-white;
  position: relative;

  .grid-container {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin: 0;
    padding: 0;
  }

  // 滚动模式样式
  &.scroll-mode {
    padding-bottom: 20px; // 为滚动条留出空间

    .grid-container {
      flex-wrap: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      gap: 5px;
      //padding-bottom: 5px;

      // 隐藏原生滚动条
      &::-webkit-scrollbar {
        display: none;
      }
      -ms-overflow-style: none;
      scrollbar-width: none;
    }

    .grid-item {
      flex-shrink: 0;
      width: 70px;
    }
  }

  // 自定义滚动条
  .custom-scrollbar {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: rgba(255, 122, 10, 0.2);
    border-radius: 2px;

    .scrollbar-thumb {
      position: absolute;
      top: 0;
      height: 100%;
      background-color: @theme-color;
      border-radius: 2px;
      transition: all 0.2s ease;
      min-width: 12px;
    }
  }

  .grid-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: v-bind(itemWidth);
    box-sizing: border-box;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 5px 0;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
      border-radius: 8px;
      transform: translateY(-1px);

      .item-icon {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
      }
    }

    &:active {
      transform: translateY(0);
      background-color: rgba(0, 0, 0, 0.04);
      border-radius: 8px;

      .item-icon {
        transform: scale(0.95);
      }
    }

    .item-content {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
    }

    .item-icon {
      width: 45px;
      height: 45px;
      margin-bottom: 3px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      img {
        width: 34px;
        height: 34px;
        object-fit: contain;
      }

      .icon-placeholder {
        color: @text-color-tertiary;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .item-title {
      font-size: @font-size-12;
      font-weight: @font-weight-500;
      color: @text-color-primary;
      text-align: center;
      line-height: 1.2;
      max-width: 100%;
      .ellipsis();
    }

    .item-subtitle {
      font-size: @font-size-11;
      color: @text-color-tertiary;
      text-align: center;
      margin-top: @radius-2;
      line-height: 1.2;
      max-width: 100%;
      .ellipsis();
    }

    .item-badge {
      position: absolute;
      top: -@radius-2;
      right: -@radius-2;
      background: linear-gradient(135deg, @color-red 0%, darken(@color-red, 10%) 100%);
      color: @color-white;
      font-size: @font-size-11;
      font-weight: @font-weight-600;
      padding: @radius-2 @radius-6;
      border-radius: @radius-8;
      min-width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 1px 3px rgba(255, 107, 107, 0.3);
      transform: scale(0.9);
    }

    &.grid-item-more {
      .item-icon {
        background: linear-gradient(135deg, @bg-color-gray 0%, darken(@bg-color-gray, 5%) 100%);
        border: 2px dashed @divider-color-base;
        box-shadow: none;

        &::after {
          content: '';
          width: 20px;
          height: 20px;
          background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2'%3E%3Cpath d='M12 5v14M5 12h14'/%3E%3C/svg%3E") no-repeat center;
          background-size: contain;
        }
      }

      .item-title {
        color: @text-color-secondary;
      }

      &:hover .item-icon {
        background: linear-gradient(135deg, darken(@bg-color-gray, 5%) 0%, darken(@bg-color-gray, 10%) 100%);
        border-color: darken(@divider-color-base, 10%);
      }
    }
  }
}

.grid-menu {
  &.columns-2 {
    .grid-item {
      flex-direction: row;
      text-align: left;
      padding: 10px 0;

      .item-content {
        flex-direction: row;
        align-items: center;
        width: 100%;
        padding: 0 10px;
      }

      .item-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 0;
        margin-right: @radius-12;
        flex-shrink: 0;

        img {
          width: 36px;
          height: 36px;
        }
      }

      .item-title {
        font-size: @font-size-14;
        text-align: left;
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
      }

      .item-subtitle {
        text-align: left;
        margin-top: @radius-4;
      }
    }
  }

  // 占位符样式
  .grid-item-placeholder {
    pointer-events: none;
    visibility: hidden;
  }
}
</style>

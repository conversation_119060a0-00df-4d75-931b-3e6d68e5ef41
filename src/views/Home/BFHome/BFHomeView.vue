<template>
  <div class="bf-home">
    <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirect-to-search="true" redirect-url="/search"
      @search="handleSearch" />

    <div class="banner-container">
      <transition name="skeleton-fade" mode="out-in">
        <BannerSkeleton v-if="skeletonStates.banner" key="banner-skeleton" />
        <GoodsSwiper v-else-if="headerBannerList.length > 0" key="banner-content" :imageList="headerBannerList"
          mode="landscape" paginationType="fraction" :autoplay="true" :loop="true" @image-click="handleBannerClick" />
      </transition>
    </div>

    <div class="grid-menu-container">
      <transition name="skeleton-fade" mode="out-in">
        <GridMenuSkeleton v-if="skeletonStates.gridMenu" key="grid-skeleton" />
        <IconGrid v-else-if="gridMenuItems.length > 0" key="grid-content" :items="gridMenuItems" :columns="5"
          :show-more="true" :max-items="10" @item-click="handleGridItemClick" @more-click="handleMoreClick" />
      </transition>
    </div>

    <SectionContainer title="各县销冠">
      <van-list :loading="false" :finished="true" :immediate-check="false">
        <div class="waterfall-container">
          <transition name="waterfall-fade" mode="out-in">
            <WaterfallSkeleton v-if="skeletonStates.limited" :skeleton-count="6" key="limited-skeleton" />
            <Waterfall v-else-if="limitedList.length > 0" ref="limitedWaterfallRef" key="limited-waterfall"
              :list="limitedList" :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="0"
              :animationDelay="0" :backgroundColor="'transparent'" :horizontalOrder="true" :lazyload="true">
              <template #default="{ item }">
                <ProductCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
              </template>
            </Waterfall>
          </transition>
        </div>

        <transition name="fade-up">
          <div class="load-more-container"
            v-if="limitedList.length > 0 && !limitedFinished && limitedButtonCanShow && !skeletonStates.limited">
            <WoButton type="text" :disabled="limitedLoading" @click="handleLimitedLoadMore" class="load-more-button">
              {{ limitedLoading ? '加载中...' : '加载更多' }}
            </WoButton>
          </div>
        </transition>

        <transition name="fade-up">
          <div class="no-more-text" v-if="limitedList.length > 0 && limitedFinished && !skeletonStates.limited">
            <span>没有更多了</span>
          </div>
        </transition>
      </van-list>
    </SectionContainer>

    <SectionContainer title="新上好物">
      <div class="horizontal-scroll-container">
        <transition name="skeleton-fade" mode="out-in">
          <HorizontalScrollSkeleton v-if="skeletonStates.newer" :skeleton-count="5" key="newer-skeleton" />
          <div v-else-if="newerList.length > 0" key="newer-content" class="horizontal-scroll-wrapper">
            <div class="goods-item" v-for="item in newerList" :key="item.goodsId" @click="handleGoodsClick(item)">
              <ProductCard :goods-info="item" @click="handleGoodsClick(item)" />
            </div>
          </div>
        </transition>
      </div>
    </SectionContainer>

    <SectionContainer title="爆款好物">
      <van-list :loading="false" :finished="true" :immediate-check="false">
        <div class="waterfall-container">
          <transition name="waterfall-fade" mode="out-in">
            <WaterfallSkeleton v-if="skeletonStates.hotProducts" :skeleton-count="6" key="hot-skeleton" />
            <Waterfall v-else-if="hotProductsList.length > 0" ref="hotProductsWaterfallRef" key="hot-waterfall"
              :list="hotProductsList" :breakpoints="breakpoints" :hasAroundGutter="false" :animationDuration="0"
              :animationDelay="0" :backgroundColor="'transparent'" :horizontalOrder="true" :lazyload="true">
              <template #default="{ item }">
                <ProductCard :key="item.goodsId" :goods-info="item" @click="handleGoodsClick(item)" />
              </template>
            </Waterfall>
          </transition>
        </div>

        <transition name="fade-up">
          <div class="load-more-container"
            v-if="hotProductsList.length > 0 && !hotProductsFinished && hotProductsButtonCanShow && !skeletonStates.hotProducts">
            <WoButton type="text" :disabled="hotProductsLoading" @click="handleHotProductsLoadMore"
              class="load-more-button">
              {{ hotProductsLoading ? '加载中...' : '加载更多' }}
            </WoButton>
          </div>
        </transition>

        <transition name="fade-up">
          <div class="no-more-text"
            v-if="hotProductsList.length > 0 && hotProductsFinished && !skeletonStates.hotProducts">
            <span>没有更多了</span>
          </div>
        </transition>
      </van-list>
    </SectionContainer>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { throttle, debounce } from 'lodash-es'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import SearchHeader from '@components/Common/SearchHeader.vue'
import IconGrid from '@components/Common/Home/IconGrid.vue'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom, isWopay } from 'commonkit'
import SectionContainer from '@components/Common/Home/SectionContainer.vue'
import ProductCard from '@components/Common/Home/ProductCard.vue'
import { fenToYuan } from '@utils/amount.js'
import { useRouter } from 'vue-router'
import { closeToast, showLoadingToast } from 'vant'
import BannerSkeleton from '@views/Home/components/Skeleton/BannerSkeleton.vue'
import GridMenuSkeleton from '@views/Home/components/Skeleton/GridMenuSkeleton.vue'
import WaterfallSkeleton from '@views/Home/components/Skeleton/WaterfallSkeleton.vue'
import HorizontalScrollSkeleton from '@views/Home/components/Skeleton/HorizontalScrollSkeleton.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import { getDefaultBreakpoints } from '@/config/responsive.js'
import GoodsSwiper from "@components/Common/GoodsSwiper.vue";

const router = useRouter()
const searchKeyword = ref('')
const headerBannerList = ref([])
const gridMenuItems = ref([])

const limitedList = ref([])
const limitedLoading = ref(false)
const limitedFinished = ref(false)
const limitedCurrentPage = ref(1)
const limitedPageSize = ref(10)
const limitedButtonCanShow = ref(false)

const newerList = ref([])
const newerLoading = ref(false)

const hotProductsList = ref([])
const hotProductsLoading = ref(false)
const hotProductsFinished = ref(false)
const hotProductsCurrentPage = ref(1)
const hotProductsPageSize = ref(10)
const hotProductsButtonCanShow = ref(false)

const skeletonStates = ref({
  banner: true,
  gridMenu: true,
  limited: true,
  newer: true,
  hotProducts: true
})

const breakpoints = ref(getDefaultBreakpoints())

const limitedWaterfallRef = ref(null)
const hotProductsWaterfallRef = ref(null)

const channelFilterd = list => {
  if (isUnicom) {
    return list.filter(item => item.channelType === '1')
  } else if (isWopay) {
    return list.filter(item => item.channelType === '0')
  } else {
    return list.filter(item => item.channelType === '2')
  }
}



const getHeaderBannerList = async () => {
  const [err, json] = await getBannerInfo({ bizCode: getBizCode('QUERY'), showPage: 1 })
  if (!err) {
    const bannerData = channelFilterd(json).map(item => ({
      type: 'image',
      url: item.imgUrl,
      alt: item.bannerChName,
      linkUrl: item.url,
    }))
    headerBannerList.value = bannerData
  }

  skeletonStates.value.banner = false
}

const getIconList = async () => {
  const [err, json] = await getIconInfo({
    bizCode: getBizCode('QUERY'),
    channel: curChannelBiz.get(),
    showPage: 2
  })

  if (!err) {
    if (json) {
      const iconData = json.map(item => ({
        title: item.chName || item.title,
        subtitle: item.iconSubTitle || item.subtitle,
        icon: item.imgUrl || item.icon,
        url: item.url,
        badge: item.badge || item.iconBadge
      }))
      gridMenuItems.value = iconData.slice(0, 4)
    } else {
      gridMenuItems.value = []
    }
  }

  skeletonStates.value.gridMenu = false
}

const handleSearch = debounce(() => {
  // 搜索逻辑可以在这里实现
}, 300)

const handleBannerClick = ({ item }) => {
  if (item.linkUrl) {
    window.location.href = item.linkUrl
  }
}

const handleGridItemClick = ({ item }) => {
  if (item.url) {
    window.location.href = item.url
  }
}

const handleMoreClick = () => {
}

const getLimitedList = async (isLoadMore = false) => {
  if (limitedLoading.value) return

  limitedLoading.value = true
  if (!isLoadMore) {
    showLoadingToast()
  }

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: limitedCurrentPage.value,
    page_size: limitedPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_LIMITED_GOODS_ID
  })

  if (!isLoadMore) {
    closeToast()
  }

  if (!err && json && Array.isArray(json)) {
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    if (isLoadMore) {
      limitedList.value = [...limitedList.value, ...newItems]
    } else {
      limitedList.value = newItems
      skeletonStates.value.limited = false
      limitedButtonCanShow.value = true
    }

    if (json.length === 0) {
      limitedFinished.value = true
    } else {
      limitedFinished.value = false
    }

    if (isLoadMore) {
      limitedCurrentPage.value++
    } else {
      limitedCurrentPage.value = 2
    }

  } else {
    limitedFinished.value = true
    skeletonStates.value.limited = false
  }

  limitedLoading.value = false
}
const handleLimitedLoadMore = throttle(() => {
  if (!limitedFinished.value && !limitedLoading.value) {
    getLimitedList(true)
  }
}, 300)

const getNewerList = async () => {
  if (newerLoading.value) return

  newerLoading.value = true

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: 1,
    page_size: 10,
    id: import.meta.env.VITE_FP_HOME_PAGE_NEWER_GOODS_ID
  })

  if (!err && json && Array.isArray(json)) {
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    newerList.value = newItems
  }

  skeletonStates.value.newer = false

  newerLoading.value = false
}

const getHotProductsList = async (isLoadMore = false) => {
  if (hotProductsLoading.value) return

  hotProductsLoading.value = true

  if (isLoadMore) {
    showLoadingToast()
  }

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: hotProductsCurrentPage.value,
    page_size: hotProductsPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_GUESS_GOODS_ID
  })

  if (isLoadMore) {
    closeToast()
  }

  if (!err && json && Array.isArray(json)) {
    const newItems = json.map(item => ({
      name: item.name || item.goodName,
      price: fenToYuan(item.skuList[0].price) || item.goodsPrice,
      sales: item.skuList[0].realSaleVolume || item.salesCount || 0,
      goodsId: item.id || item.goodsId,
      image: item.listImageUrl || item.image,
      spec: item.spec || item.goodsSpec,
    }))

    if (isLoadMore) {
      hotProductsList.value = [...hotProductsList.value, ...newItems]
    } else {
      hotProductsList.value = newItems
      skeletonStates.value.hotProducts = false
      hotProductsButtonCanShow.value = true
    }

    if (json.length === 0) {
      hotProductsFinished.value = true
    } else {
      hotProductsFinished.value = false
    }

    if (isLoadMore) {
      hotProductsCurrentPage.value++
    } else {
      hotProductsCurrentPage.value = 2
    }
  } else {
    hotProductsFinished.value = true
    skeletonStates.value.hotProducts = false
  }

  hotProductsLoading.value = false
}

const handleHotProductsLoadMore = throttle(() => {
  if (!hotProductsFinished.value && !hotProductsLoading.value) {
    getHotProductsList(true)
  }
}, 300)



const handleGoodsClick = (goodsInfo) => {
  if (goodsInfo.goodsId) {
    router.push(`/goodsdetail/${goodsInfo.goodsId}`)
  }
}




onMounted(() => {
  getHeaderBannerList()
  getIconList()
  getLimitedList(false)
  getNewerList()
  getHotProductsList(false)
})
</script>

<style scoped lang="less">
.bf-home {
  width: 100vw;
  height: 100%;
  overflow: auto;
  background: @bg-color-gray;

  .banner-container {
    margin: @radius-8 @radius-12;
    border-radius: @radius-12;
    overflow: hidden;
  }

  .grid-menu-container {
    //background: @bg-color-white;
    border-radius: @radius-12;
    margin: @radius-8 @radius-12;
  }

  .horizontal-scroll-container {
    position: relative;
    min-height: 180px;
    display: flex;
    align-items: center;

    .horizontal-scroll-wrapper {
      display: flex;
      gap: @radius-12;
      overflow-x: auto;
      //padding-bottom: @radius-8;
      scroll-behavior: smooth;
      width: 100%;
      .no-scrollbar();

      .goods-item {
        flex: 0 0 130px;
        cursor: pointer;

        &:last-child {
          margin-right: @radius-12;
        }
      }
    }
  }

  .waterfall-container {
    position: relative;
    //min-height: 500px;

    :deep(.vue-waterfall) {
      opacity: 1;
      transition: opacity 0.3s ease;
    }
  }

  .waterfall-container {
    position: relative;
    //min-height: 500px;

    :deep(.vue-waterfall) {
      opacity: 1;
      transition: opacity 0.3s ease;
    }
  }

  // 瀑布流过渡动画
  .waterfall-fade-enter-active,
  .waterfall-fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .waterfall-fade-enter-from,
  .waterfall-fade-leave-to {
    opacity: 0;
  }

  .waterfall-fade-enter-to,
  .waterfall-fade-leave-from {
    opacity: 1;
  }

  .loading-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
    color: @text-color-tertiary;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;

    .horizontal-scroll-container & {
      position: static;
      transform: none;
      padding: 60px 0;
    }
  }

  .home-block {
    &:not(:first-child) {
      margin-top: @radius-10;
    }

    .content {
      position: relative;
      overflow: hidden;
    }
  }

  .load-more-container {
    display: flex;
    justify-content: center;
    align-items: center;

    .load-more-button {
      min-width: 120px;
      height: @button-height-36;
      font-size: @font-size-14;

      &.wo-button-text {
        background-color: transparent;
        border-radius: @radius-18;

        &:active {
          opacity: @opacity-07;
        }

        &.wo-button-disabled {
          opacity: 0.6;
          cursor: not-allowed;

          &:active {
            transform: none;
          }
        }
      }
    }
  }

  .no-more-text {
    padding: 20px 0 16px;
    text-align: center;

    span {
      font-size: @font-size-14;
      color: @text-color-tertiary;
    }
  }
}
</style>
